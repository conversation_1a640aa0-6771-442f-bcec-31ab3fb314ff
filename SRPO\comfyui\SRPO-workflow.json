{"id": "3107ea10-7994-4c54-b347-b27a8b767145", "revision": 0, "last_node_id": 39, "last_link_id": 118, "nodes": [{"id": 26, "type": "FluxGuidance", "pos": [480, 144], "size": [317.4000244140625, 58], "flags": {}, "order": 10, "mode": 0, "inputs": [{"name": "conditioning", "type": "CONDITIONING", "link": 41}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [42]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.47", "Node name for S&R": "FluxGuidance"}, "widgets_values": [3.5], "color": "#233", "bgcolor": "#355"}, {"id": 22, "type": "BasicGuider", "pos": [576, 48], "size": [222.3482666015625, 46], "flags": {}, "order": 12, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 54}, {"name": "conditioning", "type": "CONDITIONING", "link": 42}], "outputs": [{"name": "GUIDER", "type": "GUIDER", "slot_index": 0, "links": [30]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.47", "Node name for S&R": "BasicGuider"}, "widgets_values": []}, {"id": 35, "type": "PrimitiveNode", "pos": [672, 480], "size": [210, 82], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "INT", "type": "INT", "widget": {"name": "height"}, "slot_index": 0, "links": [113, 114]}], "title": "height", "properties": {"Run widget replace on values": false}, "widgets_values": [720, "fixed"], "color": "#323", "bgcolor": "#535"}, {"id": 25, "type": "RandomNoise", "pos": [480, 768], "size": [315, 82], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "NOISE", "type": "NOISE", "links": [37]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.47", "Node name for S&R": "RandomNoise"}, "widgets_values": [1234, "fixed"], "color": "#2a363b", "bgcolor": "#3f5159"}, {"id": 16, "type": "KSamplerSelect", "pos": [485.88037109375, 892.5553588867188], "size": [315, 58], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"name": "SAMPLER", "type": "SAMPLER", "links": [19]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.47", "Node name for S&R": "KSamplerSelect"}, "widgets_values": ["euler"]}, {"id": 30, "type": "ModelSamplingFlux", "pos": [819.402099609375, 769.8795166015625], "size": [315, 130], "flags": {}, "order": 11, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 56}, {"name": "width", "type": "INT", "widget": {"name": "width"}, "link": 115}, {"name": "height", "type": "INT", "widget": {"name": "height"}, "link": 114}], "outputs": [{"name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [54, 55]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.47", "Node name for S&R": "ModelSamplingFlux"}, "widgets_values": [1.15, 0.5, 1280, 720]}, {"id": 17, "type": "BasicScheduler", "pos": [827.9284057617188, 619.0258178710938], "size": [315, 106], "flags": {}, "order": 13, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 55}], "outputs": [{"name": "SIGMAS", "type": "SIGMAS", "links": [20]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.47", "Node name for S&R": "BasicScheduler"}, "widgets_values": ["normal", 50, 1]}, {"id": 6, "type": "CLIPTextEncode", "pos": [384, 240], "size": [422.84503173828125, 164.31304931640625], "flags": {}, "order": 7, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 10}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [41]}], "title": "CLIP Text Encode (Positive Prompt)", "properties": {"cnr_id": "comfy-core", "ver": "0.3.47", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["portrait of a girl", [false, true]], "color": "#232", "bgcolor": "#353"}, {"id": 27, "type": "EmptySD3LatentImage", "pos": [480, 624], "size": [315, 106], "flags": {}, "order": 8, "mode": 0, "inputs": [{"name": "width", "type": "INT", "widget": {"name": "width"}, "link": 112}, {"name": "height", "type": "INT", "widget": {"name": "height"}, "link": 113}], "outputs": [{"name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [116]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.47", "Node name for S&R": "EmptySD3LatentImage"}, "widgets_values": [1280, 720, 1]}, {"id": 10, "type": "VAELoader", "pos": [117.68590545654297, 618.4750366210938], "size": [311.81634521484375, 60.429901123046875], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"name": "VAE", "type": "VAE", "slot_index": 0, "links": [12]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.47", "Node name for S&R": "VAELoader"}, "widgets_values": ["ae.safetensors"]}, {"id": 11, "type": "DualCLIPLoader", "pos": [94.37985229492188, 431.09332275390625], "size": [315, 130], "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [{"name": "CLIP", "type": "CLIP", "slot_index": 0, "links": [10]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.47", "Node name for S&R": "DualCLIPLoader"}, "widgets_values": ["t5xxl_fp16.safetensors", "clip_l.safetensors", "flux", "default"]}, {"id": 8, "type": "VAEDecode", "pos": [904.7182006835938, 503.7352600097656], "size": [210, 46], "flags": {}, "order": 15, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 24}, {"name": "vae", "type": "VAE", "link": 12}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [9]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.47", "Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 34, "type": "PrimitiveNode", "pos": [448.2789001464844, 481.0135192871094], "size": [210, 82], "flags": {}, "order": 5, "mode": 0, "inputs": [], "outputs": [{"name": "INT", "type": "INT", "widget": {"name": "width"}, "slot_index": 0, "links": [112, 115]}], "title": "width", "properties": {"Run widget replace on values": false}, "widgets_values": [1280, "fixed"], "color": "#323", "bgcolor": "#535"}, {"id": 13, "type": "SamplerCustomAdvanced", "pos": [843.1868896484375, 299.7455139160156], "size": [272.3617858886719, 124.53733825683594], "flags": {}, "order": 14, "mode": 0, "inputs": [{"name": "noise", "type": "NOISE", "link": 37}, {"name": "guider", "type": "GUIDER", "link": 30}, {"name": "sampler", "type": "SAMPLER", "link": 19}, {"name": "sigmas", "type": "SIGMAS", "link": 20}, {"name": "latent_image", "type": "LATENT", "link": 116}], "outputs": [{"name": "output", "type": "LATENT", "slot_index": 0, "links": [24]}, {"name": "denoised_output", "type": "LATENT", "links": null}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.47", "Node name for S&R": "SamplerCustomAdvanced"}, "widgets_values": []}, {"id": 38, "type": "PrimitiveNode", "pos": [127.22330474853516, 137.1959228515625], "size": [210, 106], "flags": {}, "order": 6, "mode": 0, "inputs": [], "outputs": [{"name": "COMBO", "type": "COMBO", "widget": {"name": "unet_name"}, "links": [117]}], "title": "unet_name", "properties": {"Run widget replace on values": false}, "widgets_values": ["SRPO/diffusion_pytorch_model.safetensors", "fixed", ""]}, {"id": 12, "type": "UNETLoader", "pos": [62.84854507446289, 294.************], "size": [315, 82], "flags": {}, "order": 9, "mode": 0, "inputs": [{"name": "unet_name", "type": "COMBO", "widget": {"name": "unet_name"}, "link": 117}], "outputs": [{"name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [56]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.47", "Node name for S&R": "UNETLoader"}, "widgets_values": ["SRPO/diffusion_pytorch_model.safetensors", "default"], "color": "#223", "bgcolor": "#335"}, {"id": 9, "type": "SaveImage", "pos": [1170.7294921875, 165.6167755126953], "size": [992.8073120117188, 656.9705810546875], "flags": {}, "order": 16, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 9}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.47"}, "widgets_values": ["ComfyUI"]}], "links": [[9, 8, 0, 9, 0, "IMAGE"], [10, 11, 0, 6, 0, "CLIP"], [12, 10, 0, 8, 1, "VAE"], [19, 16, 0, 13, 2, "SAMPLER"], [20, 17, 0, 13, 3, "SIGMAS"], [24, 13, 0, 8, 0, "LATENT"], [30, 22, 0, 13, 1, "GUIDER"], [37, 25, 0, 13, 0, "NOISE"], [41, 6, 0, 26, 0, "CONDITIONING"], [42, 26, 0, 22, 1, "CONDITIONING"], [54, 30, 0, 22, 0, "MODEL"], [55, 30, 0, 17, 0, "MODEL"], [56, 12, 0, 30, 0, "MODEL"], [112, 34, 0, 27, 0, "INT"], [113, 35, 0, 27, 1, "INT"], [114, 35, 0, 30, 2, "INT"], [115, 34, 0, 30, 1, "INT"], [116, 27, 0, 13, 4, "LATENT"], [117, 38, 0, 12, 0, "COMBO"]], "groups": [], "config": {}, "extra": {"ds": {"scale": 1, "offset": [-88.38370132446289, -156.26953125]}, "frontendVersion": "1.23.4", "groupNodes": {}}, "version": 0.4}